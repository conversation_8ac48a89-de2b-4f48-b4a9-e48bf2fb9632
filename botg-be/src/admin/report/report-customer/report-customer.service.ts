import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { CrudRequest } from 'src/core/crud/crud';
import {
  Between,
  Brackets,
  In,
  Less<PERSON>han,
  LessThanOrEqual,
  More<PERSON>han,
  <PERSON><PERSON><PERSON><PERSON>r<PERSON>qual,
  Not,
  Repository,
} from 'typeorm';
import { Customer } from 'src/admin/customer/customer.entity';
import * as moment from 'moment';
import { Credit } from 'src/admin/credit/credit.entity';
import { ReportCustomerMembershipQueryDTO } from './dto/report-customer-membership.dto';
import { CreditHistory } from 'src/admin/credit-history/credit-history.entity';
import { Appointment } from 'src/admin/appointment/appointment.entity';
import { ReportQueryDTO } from '../dto/report.dto';
import { Invoice } from 'src/admin/invoice/invoice.entity';
import {
  CreditType,
  InvoiceStatus,
  OpeningClosingCreditType,
  PrepaidFilter,
  ProductType,
  CreditStatus,
} from 'src/core/enums/entity';
import { getCustomPaginationLimit } from 'src/core/common/common.utils';
import { BaseCrudService } from 'src/core/base/base-crud.service';
import { ReportCustomerPrepaidQueryDTO } from './dto/report-customer-prepaid.dto';
import { ReportOpeningClosingCreditQueryDTO } from './dto/report-opening-closing-credit.dto';
import { CreditSetting } from 'src/admin/settings/credit/credit-setting.entity';
import Decimal from 'decimal.js';
import { MembershipHistory } from 'src/admin/credit-history/membership-history.entity';
import { PaymentMethodType } from 'src/admin/payment-method/payment-method.entity';

@Injectable()
export class ReportCustomerService extends BaseCrudService<Customer> {
  constructor(
    @InjectRepository(Customer)
    private customerRepo: Repository<Customer>,
    @InjectRepository(Credit)
    private creditRepo: Repository<Credit>,
    @InjectRepository(CreditHistory)
    private creditHistoryRepo: Repository<CreditHistory>,
    @InjectRepository(Appointment)
    private appointmentRepo: Repository<Appointment>,
    @InjectRepository(Invoice)
    private invoiceRepo: Repository<Invoice>,
    @InjectRepository(CreditSetting)
    private creditSettingRepo: Repository<CreditSetting>,
  ) {
    super(customerRepo);
  }

  async getReportCustomerList(
    req: CrudRequest,
    { keySearch, page, limit, clientZoneName }: ReportQueryDTO,
    isExport = false,
  ) {
    // const { page, offset, limit, sort } = req?.parsed;

    const queryBuilder = this.customerRepo.createQueryBuilder('customer');
    queryBuilder.leftJoin('customer.gender', 'gender');
    queryBuilder.leftJoin('customer.preferreds', 'preferreds');
    queryBuilder.leftJoin('customer.nationality', 'nationality');
    if (keySearch) {
      queryBuilder.andWhere(
        new Brackets((subQuery) =>
          subQuery
            .where(
              "(CONCAT(TRIM(customer.firstName), ' ', TRIM(customer.lastName)) ILIKE :name)",
              { name: `%${keySearch}%` },
            )
            .orWhere('customer.code::text ILIKE :code', {
              code: `%${keySearch}%`,
            })
            .orWhere('customer.phone ILIKE :phone', {
              phone: `%${keySearch}%`,
            })
            .orWhere('customer.nric ILIKE :nric', {
              nric: `%${keySearch}%`,
            }),
        ),
      );
    }
    queryBuilder.addSelect(['gender.id', 'gender.name']);
    queryBuilder.addSelect([
      'preferreds.id',
      'preferreds.fullName',
      'preferreds.displayName',
    ]);
    queryBuilder.addSelect(['nationality.id', 'nationality.name']);
    queryBuilder.orderBy('customer.code', 'ASC');
    const qLimit = getCustomPaginationLimit(limit);
    const offset = (page - 1) * limit || 0;
    if (!isExport) {
      queryBuilder.take(qLimit).skip(offset);
    }
    const [customerAll, total] = await queryBuilder.getManyAndCount();
    if (isExport) {
      return customerAll.map((item, index) => {
        return {
          order: index + 1,
          firstName: item.firstName,
          lastName: item.lastName,
          code: item.code,
          email: item.email,
          phone: item.phone,
          address: item.address,
          gender: item.gender?.name,
          birthDay: item.birthDay
            ? moment.tz(item.birthDay, clientZoneName).format('DD/MM/YYYY')
            : '',
          preferreds: item.preferreds
            ? item.preferreds.map((preferred) => preferred.fullName).join(', ')
            : '',
          membershipNo: item.membershipNo,
          firstVisit: item.firstVisit
            ? moment.tz(item.firstVisit, clientZoneName).format('DD/MM/YYYY')
            : '',
          nric: item.nric,
          nationality: item.nationality?.name,
          remark: item.remark,
        };
      });
    }
    return this.createPageInfo<Customer>(
      customerAll,
      total,
      qLimit || total,
      offset || 0,
    );
  }

  async getReportCustomerMembership(
    req: CrudRequest,
    {
      keySearch,
      startDate,
      endDate,
      creditType,
      page,
      limit,
      clientZoneName,
    }: ReportCustomerMembershipQueryDTO,
    isExport = false,
  ) {
    // const { page, offset, limit, sort } = req?.parsed;

    // Always use startOf('day') and endOf('day') for date range
    let start = startDate ? moment(startDate) : moment();
    let end = endDate ? moment(endDate) : moment();
    // If moment is invalid, fallback to today
    if (!start.isValid()) start = moment();
    if (!end.isValid()) end = moment();
    const utcStartTime = start
      .tz(clientZoneName)
      .startOf('day')
      .format('YYYY-MM-DDTHH:mm:ss.SSS');
    const utcEndTime = end
      .tz(clientZoneName)
      .endOf('day')
      .format('YYYY-MM-DDTHH:mm:ss.SSS');

    const queryCredit = this.creditRepo
      .createQueryBuilder('credit')
      .leftJoinAndSelect('credit.customer', 'customer')
      .leftJoinAndSelect('credit.creditSetting', 'creditSetting')
      .where('(credit.expiryDate BETWEEN :startTime AND :endTime)', {
        startTime: utcStartTime,
        endTime: utcEndTime,
      });
    if (keySearch) {
      queryCredit.andWhere(
        new Brackets((subQuery) =>
          subQuery
            .where(
              "(CONCAT(TRIM(customer.firstName), ' ', TRIM(customer.lastName)) ILIKE :name)",
              { name: `%${keySearch}%` },
            )
            .orWhere('customer.code::text ILIKE :code', {
              code: `%${keySearch}%`,
            })
            .orWhere('customer.nric::text ILIKE :nric', {
              nric: `%${keySearch}%`,
            })
            .orWhere('customer.phone::text ILIKE :phone', {
              phone: `%${keySearch}%`,
            })
            .orWhere('customer."membershipNo" ILIKE :membershipNo', {
              membershipNo: `%${keySearch}%`,
            }),
        ),
      );
    }
    if (creditType) {
      const creditTypeCondition = creditType.split(',');
      queryCredit.andWhere('creditSetting.id IN (:...creditTypeCondition)', {
        creditTypeCondition,
      });
    }
    queryCredit.orderBy('customer.code', 'ASC');
    const qLimit = getCustomPaginationLimit(limit);
    const offset = (page - 1) * limit || 0;
    if (!isExport) {
      queryCredit.take(qLimit).skip(offset);
    }
    const [creditList, total] = await queryCredit.getManyAndCount();

    let modifiedCreditList = [];
    if (creditList.length > 0) {
      const creditPromises = creditList.map(async (credit) => {
        const creditHistory = await this.creditHistoryRepo.findOne({
          where: {
            credit: {
              id: credit.id,
            },
            isMembershipPkg: true,
          },
          order: {
            created: 'DESC',
          },
        });

        let membershipName = null;
        if (creditHistory) {
          const detail: any = creditHistory.detail;
          if (detail && detail.orders.length > 0) {
            const orders: any[] = detail.orders;
            for (const order of orders) {
              if (order?.items && order?.items.length > 0) {
                for (const item of order.items) {
                  if (
                    (item?.product?.type === 'MEMBERSHIP' ||
                      item?.product?.type === ProductType.MEMBERSHIP) &&
                    item?.product?.name
                  ) {
                    membershipName = item.product.name;
                    break;
                  }
                }
                if (membershipName) break;
              } else if (
                (order?.product?.type === 'MEMBERSHIP' ||
                  order?.product?.type === ProductType.MEMBERSHIP) &&
                order?.product?.name
              ) {
                membershipName = order.product.name;
                break;
              }
            }
          }
        }

        let lastVisit = null;
        const appointmentLastVisit = await this.appointmentRepo.findOne({
          where: {
            customer: {
              id: credit?.customer?.id,
            },
            checkIn: LessThan(new Date()),
          },
          order: {
            checkIn: 'DESC',
          },
        });
        if (appointmentLastVisit) {
          lastVisit = appointmentLastVisit?.checkIn;
        }

        let nextAppointment = null;
        const appointmentNextVisit = await this.appointmentRepo.findOne({
          where: {
            customer: {
              id: credit?.customer?.id,
            },
            startTime: MoreThan(new Date()),
          },
          order: {
            startTime: 'ASC',
          },
        });

        if (appointmentNextVisit) {
          nextAppointment = appointmentNextVisit?.startTime;
        }

        const amount = Math.abs(creditHistory.paid || 0);
        let isMembershipPkg = creditHistory.isMembershipPkg;
        if (creditHistory.isRefund) {
          isMembershipPkg = true;
        }

        return {
          ...credit,
          membershipName,
          lastVisit,
          nextAppointment,
          isMembershipPkg,
          amount,
        };
      });

      modifiedCreditList = await Promise.all(creditPromises);
    }

    if (isExport) {
      return modifiedCreditList.map((item, index) => {
        return {
          order: index + 1,
          customerInfo:
            item.customer?.code +
            ' ' +
            item.customer?.firstName +
            ' ' +
            item.customer?.lastName,
          nric: item.customer?.nric,
          phone: item.customer?.phone,
          membershipNo: item.customer?.membershipNo,
          type: item.creditSetting?.name,
          credit: item.creditBalance.toFixed(2),
          expiryDate: item.expiryDate
            ? moment
                .utc(item.expiryDate)
                .tz(clientZoneName)
                .subtract(1, 'day')
                .format('DD/MM/YYYY')
            : '',
          lastVisit: item.lastVisit
            ? moment(item.lastVisit).tz(clientZoneName).format('DD/MM/YYYY')
            : '',
          nextAppointment: item.nextAppointment
            ? moment(item.nextAppointment)
                .tz(clientZoneName)
                .format('DD/MM/YYYY')
            : '',
        };
      });
    }

    return this.createPageInfo<Customer>(
      modifiedCreditList,
      total,
      qLimit || total,
      offset || 0,
    );
  }

  async getReportCustomerSpending(
    req: CrudRequest,
    { keySearch, startDate, endDate, page, limit }: ReportQueryDTO,
    isExport = false,
  ) {
    // Build base query
    const queryBuilder = this.invoiceRepo
      .createQueryBuilder('invoice')
      .leftJoinAndSelect('invoice.customer', 'customer')
      .leftJoinAndSelect('invoice.invoicePayments', 'invoicePayments')
      .leftJoinAndSelect('invoicePayments.paymentMethod', 'paymentMethod')
      .where('invoice.status IN (:...statuses)', {
        statuses: [InvoiceStatus.PAID, InvoiceStatus.PART_PAID],
      })
      .andWhere('invoice.deleted IS NULL');

    // Include only real payment methods (exclude credit payments) - same logic as customer profile
    queryBuilder.andWhere('paymentMethod.type IN (:...types)', {
      types: [
        PaymentMethodType.CARD,
        PaymentMethodType.CASH,
        PaymentMethodType.FOC,
      ],
    });

    // Add date range filter if provided
    if (startDate && endDate) {
      queryBuilder.andWhere('invoice.created BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      });
    }

    // Add search filter if provided
    if (keySearch) {
      queryBuilder.andWhere(
        new Brackets((qb) => {
          qb.where('customer.firstName ILIKE :search', {
            search: `%${keySearch}%`,
          })
            .orWhere('customer.lastName ILIKE :search', {
              search: `%${keySearch}%`,
            })
            .orWhere('customer.code::text ILIKE :search', {
              search: `%${keySearch}%`,
            })
            .orWhere('customer.phone ILIKE :search', {
              search: `%${keySearch}%`,
            })
            .orWhere('customer.nric ILIKE :search', {
              search: `%${keySearch}%`,
            });
        }),
      );
    }

    // Get total count before pagination
    const totalCountQuery = queryBuilder.clone();
    const total = await totalCountQuery
      .select('COUNT(DISTINCT customer.id)', 'count')
      .getRawOne()
      .then((result) => parseInt(result.count, 10));

    // Apply pagination if not exporting
    const qLimit = getCustomPaginationLimit(limit);
    const offset = (page - 1) * limit || 0;
    if (!isExport) {
      queryBuilder.take(qLimit).skip(offset);
    }

    // Get customer spending data
    const data = await queryBuilder
      .select([
        'customer.id as "customerId"',
        'customer.code as "customerCode"',
        'customer.firstName as "firstName"',
        'customer.lastName as "lastName"',
        'customer.nric as "nric"',
        'customer.phone as "phone"',
        'customer.membershipNo as "membershipNo"',
        'SUM(invoicePayments.paid) as "totalSpending"',
      ])
      .groupBy('customer.id')
      .addGroupBy('customer.code')
      .addGroupBy('customer.firstName')
      .addGroupBy('customer.lastName')
      .addGroupBy('customer.nric')
      .addGroupBy('customer.phone')
      .addGroupBy('customer.membershipNo')
      .orderBy('customer.code', 'ASC')
      .getRawMany();

    // Calculate grand total
    const grandTotal = data.reduce(
      (sum, row) => sum + Number(row.totalSpending || 0),
      0,
    );

    // Transform data for response
    const transformedData = data.map((row, index) => ({
      order: index + 1,
      code: row.customerCode,
      firstName: row.firstName,
      lastName: row.lastName,
      customerInfo:
        `${row.customerCode} ${row.firstName} ${row.lastName}`.trim(),
      nric: row.nric || '',
      phone: row.phone || '',
      membershipNo: row.membershipNo || '',
      totalSpending: Number(row.totalSpending || 0).toFixed(2),
    }));

    // Return export format if requested
    if (isExport) {
      return [
        ...transformedData,
        {
          order: null,
          customerInfo: 'GRAND TOTAL:',
          nric: '',
          phone: '',
          membershipNo: '',
          totalSpending: grandTotal.toFixed(2),
        },
      ];
    }

    // Return paginated response
    return {
      ...this.createPageInfo(
        transformedData,
        total,
        qLimit || total,
        offset || 0,
      ),
      grandTotal: grandTotal.toFixed(2),
    };
  }

  async getReportCustomerPrepaid(
    req: CrudRequest,
    {
      keySearch,
      startDate,
      endDate,
      balanceOn = new Date(),
      prepaidFilter,
      page,
      limit,
      clientZoneName,
    }: ReportCustomerPrepaidQueryDTO,
    isExport = false,
  ) {
    const queryBuilder = this.creditRepo
      .createQueryBuilder('credit')
      .leftJoinAndSelect('credit.customer', 'customer')
      .leftJoinAndSelect('credit.creditSetting', 'creditSetting')
      .leftJoinAndSelect(
        'credit.creditHistory',
        'creditHistory',
        'creditHistory.isRefund = false',
      );

    const balanceDate = moment(balanceOn).isValid()
      ? moment(balanceOn).endOf('day').toDate()
      : moment().endOf('day').toDate();

    queryBuilder.andWhere('credit.creditBalance > 0');

    // Apply balance date filtering based on prepaidFilter
    // Credit must be issued on or before the balance date
    queryBuilder.andWhere('DATE(credit.issueDate) <= DATE(:balanceDate)', {
      balanceDate,
    });

    // Apply additional filters based on prepaidFilter and date range
    if (startDate && endDate && prepaidFilter) {
      const startMoment = moment(startDate).isValid()
        ? moment(startDate).startOf('day')
        : moment().startOf('day');
      const endMoment = moment(endDate).isValid()
        ? moment(endDate).endOf('day')
        : moment().endOf('day');

      switch (prepaidFilter) {
        case PrepaidFilter.PURCHASE_DATE:
          // For purchase date filter, still apply expiry date balance filtering
          queryBuilder.andWhere(
            'DATE(credit.expiryDate) >= DATE(:balanceDate)',
            {
              balanceDate,
            },
          );
          queryBuilder.andWhere(
            'credit.created BETWEEN :startDate AND :endDate',
            {
              startDate: startMoment.toDate(),
              endDate: endMoment.toDate(),
            },
          );
          break;
        case PrepaidFilter.EXPIRED_DATE:
          // For expired date filter, don't apply expiry date balance filtering
          // Show credits that expire within the specified range regardless of balance date
          queryBuilder.andWhere(
            'credit.expiryDate BETWEEN :startDate AND :endDate',
            {
              startDate: startMoment.toDate(),
              endDate: endMoment.toDate(),
            },
          );
          break;
        case PrepaidFilter.LAST_CONSUMED:
          // For last consumed filter, still apply expiry date balance filtering
          queryBuilder.andWhere(
            'DATE(credit.expiryDate) >= DATE(:balanceDate)',
            {
              balanceDate,
            },
          );
          queryBuilder.andWhere(
            'creditHistory.created BETWEEN :startDate AND :endDate',
            {
              startDate: startMoment.toDate(),
              endDate: endMoment.toDate(),
            },
          );
          break;
      }
    } else {
      // When no specific date filter is applied, apply standard balance date filtering
      // Credit must not be expired on the balance date
      queryBuilder.andWhere('DATE(credit.expiryDate) >= DATE(:balanceDate)', {
        balanceDate,
      });
    }

    if (keySearch) {
      queryBuilder.andWhere(
        new Brackets((qb) => {
          qb.where('customer.firstName ILIKE :search', {
            search: `%${keySearch}%`,
          })
            .orWhere('customer.lastName ILIKE :search', {
              search: `%${keySearch}%`,
            })
            .orWhere('customer.code::text ILIKE :search', {
              search: `%${keySearch}%`,
            });
        }),
      );
    }

    const qLimit = getCustomPaginationLimit(limit);
    const offset = (page - 1) * limit || 0;
    queryBuilder.orderBy('credit.expiryDate', 'DESC');
    if (!isExport) {
      queryBuilder.take(qLimit).skip(offset);
    }
    const [data, total] = await queryBuilder.getManyAndCount();

    function getPurchaseHistory(credit) {
      if (!credit.creditHistory) return null;
      return credit.creditHistory.find((h) => h.isMembershipPkg) || null;
    }
    function getLastConsumedHistory(credit) {
      if (!credit.creditHistory) return null;
      return credit.creditHistory[0] || null;
    }
    function getTotalCredit(credit): number {
      if (!credit.creditHistory || !Array.isArray(credit.creditHistory))
        return 0;

      return credit.creditHistory
        .filter((ch) => ch.isMembershipPkg === true)
        .reduce((sum, history) => {
          return sum + (history.paid || 0);
        }, 0);
    }

    const transformedData = await Promise.all(
      data.map(async (credit, index) => {
        const purchaseHistory = getPurchaseHistory(credit);
        const totalCredit = getTotalCredit(credit);
        const prepaidTotal = await this.getCreditInvoiceOfCustomer(
          credit.customer.id,
        );

        const lastConsumedHistory = getLastConsumedHistory(credit);
        const membershipName =
          purchaseHistory?.product?.name || credit.creditSetting?.name || '';
        // Get price from purchase history with discount applied
        const referenceNo = purchaseHistory?.invoice?.code || '';
        const purchaseDate = credit.created
          ? moment(credit.created).tz(clientZoneName)
          : '';
        const expiryDate = credit.expiryDate
          ? moment(credit.expiryDate).tz(clientZoneName)
          : '';
        const lastConsumed = lastConsumedHistory?.created
          ? moment(lastConsumedHistory.created).tz(clientZoneName)
          : '';
        // Set value to current valid credit balance
        const value = credit.creditBalance || 0;
        // Calculate total by adding tax to price
        const total = totalCredit;
        return {
          order: index + 1,
          id: credit.id,
          issueDate: credit.issueDate
            ? moment(credit.issueDate).format('YYYY-MM-DDTHH:mm:ssZ')
            : '',
          expiryDate,
          creditBalance: credit.creditBalance || 0,
          status: credit.status,
          customer: credit.customer,
          creditSetting: credit.creditSetting,
          membershipName,
          purchaseDate,
          referenceNo,
          price: prepaidTotal.subtotal.toFixed(2),
          lastConsumed,
          value,
          total,
          paidValue: prepaidTotal.total.toFixed(2),
        };
      }),
    );

    const grandTotal = transformedData.reduce(
      (sum, row) => {
        sum.creditBalance += Number(row.creditBalance || 0);
        sum.total += Number(row.total || 0);
        sum.value += Number(row.value || 0);
        sum.price += Number(row.price || 0);
        sum.paidValue += Number(row.paidValue || 0);
        return sum;
      },
      { creditBalance: 0, total: 0, value: 0, price: 0, paidValue: 0 },
    );

    if (isExport) {
      return [
        ...transformedData,
        {
          order: '',
          customer: 'GRAND TOTAL:',
          nric: '',
          phone: '',
          membershipNo: '',
          type: '',
          credit: grandTotal.creditBalance.toFixed(2),
          expiryDate: '',
          lastConsumed: '',
          total: grandTotal.total.toFixed(2),
          value: grandTotal.value.toFixed(2),
          price: grandTotal.price.toFixed(2),
          paidValue: grandTotal.paidValue.toFixed(2),
        },
      ];
    }

    return {
      ...this.createPageInfo(
        transformedData,
        total,
        qLimit || total,
        offset || 0,
      ),
      grandTotal: {
        creditBalance: Number(grandTotal.creditBalance.toFixed(2)),
        total: Number(grandTotal.total.toFixed(2)),
        value: Number(grandTotal.value.toFixed(2)),
        price: Number(grandTotal.price.toFixed(2)),
        paidValue: Number(grandTotal.paidValue.toFixed(2)),
      },
    };
  }

  async getCreditInvoiceOfCustomer(customerId: string): Promise<{
    subtotal: number;
    total: number;
  }> {
    const creditHistory = await this.creditHistoryRepo.find({
      where: {
        invoice: {
          customer: {
            id: customerId,
          },
        },
        isMembershipPkg: true,
      },
      relations: ['invoice', 'invoice.customer'],
    });

    const invoiceIds = creditHistory
      .map((ch) => ch.invoice?.id)
      .filter((id): id is string => !!id);

    if (invoiceIds.length === 0) {
      return { subtotal: 0, total: 0 };
    }

    const invoices = await this.invoiceRepo.find({
      where: {
        id: In(invoiceIds),
      },
    });

    const { subtotal, total } = invoices.reduce(
      (acc, invoice) => {
        acc.subtotal += invoice.totalBeforeTax || 0;
        acc.total += invoice.total || 0;
        return acc;
      },
      { subtotal: 0, total: 0 },
    );

    return { subtotal, total };
  }

  async getReportCreditHistory(
    req: CrudRequest,
    {
      keySearch,
      startDate,
      endDate,
      clientZoneName,
      page,
      limit,
    }: ReportQueryDTO,
    isExport = false,
  ) {
    const utcStartTime = startDate;
    const utcEndTime = endDate;

    const queryCreditHistory = this.creditHistoryRepo
      .createQueryBuilder('creditHistory')
      .leftJoinAndSelect('creditHistory.membership', 'membership')
      .where('(creditHistory.created BETWEEN :startTime AND :endTime)', {
        startTime: utcStartTime,
        endTime: utcEndTime,
      })
      .andWhere('"creditHistory"."isPassportHistory" = :isPassPortHistory', {
        isPassPortHistory: false,
      });

    let creditHistoryList;
    let total = 0;

    // Apply pagination only when not exporting and no keySearch
    // This is because keySearch filtering happens after data processing
    if (!isExport && !keySearch) {
      const qLimit = getCustomPaginationLimit(limit);
      const offset = (page - 1) * limit || 0;
      queryCreditHistory.take(qLimit).skip(offset);
      const [data, count] = await queryCreditHistory.getManyAndCount();
      creditHistoryList = data;
      total = count;
    } else {
      creditHistoryList = await queryCreditHistory.getMany();
      total = creditHistoryList.length;
    }

    let modifiedCreditHistoryList = [];
    if (creditHistoryList.length > 0) {
      const creditPromises = creditHistoryList.map(async (creditHistory) => {
        const detail: any = creditHistory.detail || {};
        let adjustment = null;

        if (creditHistory.isRefund) {
          const orders: any[] = Array.isArray(detail.orders)
            ? detail.orders
            : [];
          for (const order of orders) {
            if (order?.items && order?.items.length > 0) {
              for (const item of order.items) {
                if (
                  (item?.product?.type === 'MEMBERSHIP' ||
                    item?.product?.type === ProductType.MEMBERSHIP) &&
                  item?.product?.name
                ) {
                  adjustment = item.product.name;
                  break;
                }
              }
              if (adjustment) break;
            } else if (
              (order?.product?.type === 'MEMBERSHIP' ||
                order?.product?.type === ProductType.MEMBERSHIP) &&
              order?.product?.name
            ) {
              adjustment = order.product.name;
              break;
            }
          }
          if (!adjustment) adjustment = 'Refund credit';
          const customer = detail?.customer || {};
          const code = detail?.code || '';
          const referenceNo = code ? `CN${code}` : '';
          return {
            ...creditHistory,
            adjustment,
            referenceNo,
            customer,
            amount: Math.abs(creditHistory.paid || 0),
          };
        }

        // Set adjustment text to "Purge credit" for purged expired credits
        if (creditHistory.isPurgeExpired) {
          adjustment = 'Purge credit';
          // Ensure customer data is available for purge credit entries
          const customer = detail?.customer || {};
          return {
            ...creditHistory,
            adjustment,
            referenceNo: `IN${detail?.code}`,
            customer,
            amount: Math.abs(creditHistory.paid || 0),
          };
        } else {
          const orders: any[] = Array.isArray(detail.orders)
            ? detail.orders
            : [];
          for (const order of orders) {
            if (order?.items && order?.items.length > 0) {
              for (const item of order.items) {
                if (
                  (item?.product?.type === 'MEMBERSHIP' ||
                    item?.product?.type === ProductType.MEMBERSHIP) &&
                  item?.product?.name
                ) {
                  adjustment = item.product.name;
                  break;
                }
              }
              if (adjustment) break;
            } else if (
              (order?.product?.type === 'MEMBERSHIP' ||
                order?.product?.type === ProductType.MEMBERSHIP) &&
              order?.product?.name
            ) {
              adjustment = order.product.name;
              break;
            }
          }
          if (
            !adjustment &&
            creditHistory.membership &&
            creditHistory.membership.detail
          ) {
            const rootDetail = creditHistory.membership.detail;
            const rootOrders =
              rootDetail &&
              typeof rootDetail === 'object' &&
              'orders' in rootDetail &&
              Array.isArray(rootDetail.orders)
                ? rootDetail.orders
                : [];
            for (const order of rootOrders) {
              if (
                (order?.product?.type === 'MEMBERSHIP' ||
                  order?.product?.type === ProductType.MEMBERSHIP) &&
                order?.product?.name
              ) {
                adjustment = order.product.name;
                break;
              }
              if (adjustment) break;
            }
          }

          const referenceNo = detail?.code || '';
          const customer = detail?.customer || {};
          delete creditHistory.detail;
          return {
            ...creditHistory,
            adjustment,
            referenceNo: referenceNo ? `IN${referenceNo}` : '',
            customer,
            amount: Math.abs(creditHistory.paid || 0),
          };
        }
      });

      modifiedCreditHistoryList = await Promise.all(creditPromises);

      modifiedCreditHistoryList.sort((a, b) => {
        return (
          new Date(a.created || new Date()).getTime() -
          new Date(b.created || new Date()).getTime()
        );
      });

      if (keySearch && keySearch.trim() !== '') {
        const searchTerm = keySearch.trim().toLowerCase();
        modifiedCreditHistoryList = modifiedCreditHistoryList.filter((item) => {
          const customer = item?.customer || {};

          // Skip records without customer data or zero amount
          if (!customer || !item?.amount || item.amount <= 0) return false;

          const lastName = (customer?.lastName || '').toLowerCase();
          const firstName = (customer?.firstName || '').toLowerCase();
          const fullName = `${lastName} ${firstName}`.trim();
          const customerName = `${firstName} ${lastName}`.trim();
          const code = (customer?.code || '').toLowerCase();
          const refNo = (item?.referenceNo || '').toLowerCase();

          return (
            refNo.includes(searchTerm) ||
            code.includes(searchTerm) ||
            lastName.includes(searchTerm) ||
            firstName.includes(searchTerm) ||
            fullName.includes(searchTerm) ||
            customerName.includes(searchTerm)
          );
        });
      }
    }

    const grandTotal = modifiedCreditHistoryList.reduce((total, item) => {
      if (item.isMembershipPkg && !item.isRefund) {
        return total + item.amount;
      }
      if (!item.isMembershipPkg && !item.isRefund) {
        return total - item.amount;
      }
      if (item.isRefund) {
        return total - item.amount;
      }
      return total;
    }, 0);

    if (isExport) {
      modifiedCreditHistoryList.push({
        grandTotal,
        sum: 'grand_total',
      });

      return modifiedCreditHistoryList.map((item, index) => {
        return {
          order: item.sum === 'grand_total' ? '' : index + 1,
          created:
            item.sum === 'grand_total'
              ? ''
              : item.created
              ? moment
                  .tz(item.created, clientZoneName)
                  .format('DD/MM/YYYY h:mm A')
              : '',
          referenceNo: item.sum === 'grand_total' ? '' : item.referenceNo || '',
          customerInfo:
            item.sum === 'grand_total'
              ? ''
              : `${item.customer?.code || ''} ${
                  item.customer?.firstName || ''
                } ${item.customer?.lastName || ''}`.trim(),
          adjustment:
            item.sum === 'grand_total' ? 'GRAND TOTAL:' : item.adjustment || '',
          amount: (item.sum === 'grand_total'
            ? item.grandTotal
            : item.isMembershipPkg && !item.isRefund
            ? item.amount
            : -item.amount
          ).toFixed(2),
        };
      });
    }

    // For non-keySearch requests, return with pagination metadata
    if (!keySearch && !isExport) {
      const qLimit = getCustomPaginationLimit(limit);
      const offset = (page - 1) * limit || 0;
      return {
        ...this.createPageInfo(
          modifiedCreditHistoryList,
          total,
          qLimit || total,
          offset || 0,
        ),
        total: grandTotal, // Keep the grandTotal for backward compatibility
      };
    } else {
      // Keep original return format for keySearch or export
      return {
        data: modifiedCreditHistoryList,
        total: grandTotal,
      };
    }
  }

  async getReportCustomerCredit(
    req: CrudRequest,
    { keySearch, page, limit, showZero }: ReportCustomerMembershipQueryDTO,
    isExport = false,
  ) {
    showZero =
      showZero && (showZero === 'true' || showZero == true) ? true : false;
    const whereClause: Record<string, any> = {};
    const queryCredit = this.creditRepo
      .createQueryBuilder('credit')
      .leftJoinAndSelect('credit.customer', 'customer')
      .leftJoinAndSelect('credit.creditSetting', 'creditSetting');
    if (keySearch) {
      queryCredit.andWhere(
        new Brackets((subQuery) =>
          subQuery
            .where(
              "(CONCAT(TRIM(customer.firstName), ' ', TRIM(customer.lastName)) ILIKE :name)",
              { name: `%${keySearch}%` },
            )
            .orWhere('customer.code::text ILIKE :code', { code: keySearch }),
        ),
      );
    }
    const grandTotal = {
      balance: '',
      total: 0,
      usable: 0,
      creditValue: 0,
    };
    if (!showZero) {
      queryCredit.andWhere('credit.creditBalance > 0');
    }
    queryCredit.orderBy('customer.code', 'ASC');
    const qLimit = getCustomPaginationLimit(limit);
    const offset = (page - 1) * limit || 0;
    if (!isExport) {
      queryCredit.take(qLimit).skip(offset);
    }
    const [creditList, total] = await queryCredit.getManyAndCount();

    const modifiedCreditList = [];
    if (creditList.length > 0) {
      if (!showZero) {
        whereClause.usable = MoreThan(0);
      }
      whereClause.isMembershipPkg = true;
      for (const credit of creditList) {
        whereClause.credit = {};
        whereClause.credit.id = credit.id;
        whereClause.isPurgeExpired = false;

        const creditHistories = await this.creditHistoryRepo.find({
          where: whereClause,
          order: {
            created: 'ASC',
          },
          relations: ['product', 'invoice'],
        });

        if (creditHistories.length > 0) {
          // Group credit histories by invoice to identify packages purchased together
          const invoiceGroups =
            this.groupCreditHistoriesByInvoice(creditHistories);

          const itemsData = [];
          for (const creditHistory of creditHistories) {
            let membershipName = null;
            let creditValue = 0;

            const detail: any = creditHistory.detail;
            if (
              detail &&
              detail.orders &&
              Array.isArray(detail.orders) &&
              detail.orders.length > 0
            ) {
              membershipName = creditHistory?.product?.name;
              creditValue = creditHistory?.closing - creditHistory?.opening;
            }

            const usable = creditHistory.usable - creditHistory.purged;
            grandTotal.usable += usable;
            grandTotal.creditValue += creditValue;

            // Look for the latest expiry date in the same invoice group
            let expiryDate = creditHistory.expiryDate;
            if (creditHistory.invoice?.id) {
              const invoiceGroup = invoiceGroups.get(creditHistory.invoice.id);
              if (invoiceGroup && invoiceGroup.latestExpiryDate) {
                expiryDate = invoiceGroup.latestExpiryDate;
              }
            }

            itemsData.push({
              membershipName,
              balance: usable.toFixed(2) + '/' + (creditValue ?? 0).toFixed(2),
              expiryDate: expiryDate
                ? moment(expiryDate).format('DD/MM/YYYY')
                : '',
            });
          }
          grandTotal.total += credit.creditBalance;

          delete credit.total;
          modifiedCreditList.push({
            ...credit,
            itemsData,
          });
        }
      }
    }

    if (isExport) {
      const dataExport = [];
      modifiedCreditList.forEach((item) => {
        // For each customer, expand all their membership items
        if (item.itemsData && item.itemsData.length > 0) {
          item.itemsData.forEach((membershipItem, membershipIndex) => {
            dataExport.push({
              order: membershipIndex === 0 ? dataExport.length + 1 : null,
              customerInfo:
                membershipIndex === 0
                  ? `${item.customer.code ?? ''} ${
                      item.customer.firstName ?? ''
                    } ${item.customer.lastName ?? ''}`.trim()
                  : '',
              membershipName: membershipItem.membershipName,
              type: item.creditSetting?.name,
              balance: membershipItem.balance,
              total: membershipIndex === 0 ? item.creditBalance.toFixed(2) : '',
              expiryDate: membershipItem.expiryDate,
            });
          });
        } else {
          // If no membership items, still include the customer
          dataExport.push({
            order: dataExport.length + 1,
            customerInfo: `${item.customer.code ?? ''} ${
              item.customer.firstName ?? ''
            } ${item.customer.lastName ?? ''}`.trim(),
            membershipName: '',
            type: item.creditSetting?.name,
            balance: '',
            total: item.creditBalance.toFixed(2),
            expiryDate: moment(item.expiryDate).format('DD/MM/YYYY'),
          });
        }
      });

      // Add the grand total row
      dataExport.push({
        order: null,
        customerInfo: null,
        membershipName: null,
        type: 'GRAND TOTAL:',
        balance: `${grandTotal.usable.toFixed(
          2,
        )}/${grandTotal.creditValue.toFixed(2)}`,
        total: grandTotal.total.toFixed(2),
        expiryDate: null,
      });

      return dataExport;
    }

    return {
      ...this.createPageInfo<Customer>(
        modifiedCreditList,
        total,
        qLimit || total,
        offset || 0,
      ),
      grandTotal: {
        balance: `${grandTotal.usable.toFixed(
          2,
        )}/${grandTotal.creditValue.toFixed(2)}`,
        total: grandTotal.total,
      },
    };
  }

  // Helper method to group credit histories by invoice and find the latest expiry date
  private groupCreditHistoriesByInvoice(
    creditHistories: CreditHistory[],
  ): Map<string, { histories: CreditHistory[]; latestExpiryDate: Date }> {
    const invoiceGroups = new Map<
      string,
      { histories: CreditHistory[]; latestExpiryDate: Date }
    >();

    for (const history of creditHistories) {
      if (history.invoice?.id) {
        const invoiceId = history.invoice.id;

        if (!invoiceGroups.has(invoiceId)) {
          invoiceGroups.set(invoiceId, {
            histories: [],
            latestExpiryDate: null,
          });
        }

        const group = invoiceGroups.get(invoiceId);
        group.histories.push(history);

        // Update the latest expiry date
        if (history.expiryDate) {
          if (
            !group.latestExpiryDate ||
            moment(history.expiryDate).isAfter(group.latestExpiryDate)
          ) {
            group.latestExpiryDate = history.expiryDate;
          }
        }
      }
    }

    return invoiceGroups;
  }

  async getReportOpeningClosingCredit(
    req: CrudRequest,
    {
      startDate,
      endDate,
      type,
      page,
      limit,
      keySearch,
      clientZoneName,
    }: ReportOpeningClosingCreditQueryDTO,
    isExport = false,
  ) {
    // Always use startOf('day') and endOf('day') for date range
    let start = startDate ? moment(startDate) : moment();
    let end = endDate ? moment(endDate) : moment();
    // If moment is invalid, fallback to today
    if (!start.isValid()) start = moment();
    if (!end.isValid()) end = moment();
    const utcStartTime = start
      .tz(clientZoneName)
      .startOf('day')
      .format('YYYY-MM-DDTHH:mm:ss.SSS');
    const utcEndTime = end
      .tz(clientZoneName)
      .endOf('day')
      .format('YYYY-MM-DDTHH:mm:ss.SSS');
    const modifiedCreditHistoryList = [];
    const dataExport = [
      {
        title: '',
        opening: 'OPENING',
        purchase: 'PURCHASE',
        use: 'USE',
        expired: 'EXPIRED',
        closing: 'CLOSING',
        type: 'TYPE',
        value_opening: 'OPENING',
        value_purchase: 'PURCHASE',
        value_use: 'USE',
        value_expired: 'EXPIRED',
        value_closing: 'CLOSING',
        value_expiryDate: 'EXPIRY DATE',
      },
    ];

    if (!type || type === OpeningClosingCreditType.SHOW_PERIOD) {
      let queryCreditHistory =
        this.creditHistoryRepo.createQueryBuilder('credit_history');
      queryCreditHistory = queryCreditHistory
        .select([
          "TO_CHAR(credit_history.created, 'MM-YYYY') AS month",
          'MIN(credit_history.created) AS first_date',
          'MAX(credit_history.created) AS last_date',
          'SUM(CASE WHEN credit_history.isMembershipPkg = true THEN credit_history.paid ELSE 0 END) AS buy_credit',
          'SUM(CASE WHEN (credit_history.isMembershipPkg = false AND credit_history.isPurgeExpired = false) THEN credit_history.paid ELSE 0 END) AS use_credit',
          'SUM(CASE WHEN credit_history.isPurgeExpired = true THEN credit_history.paid ELSE 0 END) AS expired_credit',
          'credit_setting.creditType AS credit_type',
          'credit.creditSetting AS credit_setting',
          'credit.id AS credit_id',
          '(SELECT opening FROM credit_history AS ch2 WHERE ch2.opening = MIN(credit_history.opening) AND ch2."creditId" = credit.id LIMIT 1) AS opening',
          '(SELECT closing FROM credit_history AS ch2 WHERE ch2.closing = MAX(credit_history.closing) AND ch2."creditId" = credit.id LIMIT 1) AS closing',
        ])
        .andWhere('(credit_history.created BETWEEN :startTime AND :endTime)', {
          startTime: utcStartTime,
          endTime: utcEndTime,
        })
        .leftJoin('credit_history.credit', 'credit')
        .leftJoin('credit.creditSetting', 'credit_setting')
        .groupBy('month')
        .addGroupBy('credit.id')
        .addGroupBy('"credit_setting"."creditType"');

      const mainQueryGroupByMonth = this.creditHistoryRepo.manager
        .createQueryBuilder()
        .select('subquery.month', 'month')
        .addSelect('MIN(subquery.first_date)', 'first_date')
        .addSelect('MAX(subquery.last_date)', 'last_date')
        .addSelect('SUM(subquery.buy_credit)', 'buy_credit')
        .addSelect('SUM(subquery.use_credit)', 'use_credit')
        .addSelect('SUM(subquery.expired_credit)', 'expired_credit')
        .addSelect('subquery.credit_type', 'credit_type')
        .addSelect('array_agg(subquery.credit_setting)', 'credit_setting')
        .addSelect('array_agg(subquery.credit_id)', 'credit_id')
        .addSelect('SUM(subquery.opening)', 'opening')
        .addSelect('SUM(subquery.closing)', 'closing')
        .from(`(${queryCreditHistory.getQuery()})`, 'subquery')
        .groupBy('subquery.month')
        .addGroupBy('subquery.credit_type')
        .setParameters(queryCreditHistory.getParameters());

      const creditHistoryList = await mainQueryGroupByMonth.getRawMany();

      const oldCreditSetting = await this.creditSettingRepo.findOne({
        where: {
          creditType: CreditType.OLD,
        },
      });
      const newCreditSetting = await this.creditSettingRepo.findOne({
        where: {
          creditType: CreditType.NEW,
        },
      });

      const validCreditHistoryList = creditHistoryList.filter(
        (item) =>
          item.credit_type === 'credits' || item.credit_type === 'old_credits',
      );

      const groupByMonth = await validCreditHistoryList.reduce(
        (acc, obj, index, arr) => {
          const prevMonth = acc[acc.length - 1];
          const value =
            obj.credit_type === CreditType.OLD
              ? oldCreditSetting.price
              : newCreditSetting.price;
          const opening = prevMonth
            ? prevMonth.credits[0].quantity.closing
            : obj.opening;

          const closing =
            opening + obj.buy_credit - obj.use_credit - obj.expired_credit;

          const newObj = {
            creditType: obj.credit_type,
            quantity: {
              purchase: obj.buy_credit,
              use: obj.use_credit,
              expired: obj.expired_credit,
              opening: opening,
              closing: closing,
            },
            value: {
              purchase: obj.buy_credit * value,
              use: obj.use_credit * value,
              expired: obj.expired_credit * value,
              opening: opening * value,
              closing: closing * value,
            },
          };

          if (prevMonth && prevMonth.month === obj.month) {
            prevMonth.credits.push(newObj);
          } else {
            acc.push({
              month: obj.month,
              first_date: obj.first_date,
              last_date: obj.last_date,
              credits: [newObj],
            });
          }
          return acc;
        },
        [],
      );

      const totalData = creditHistoryList.reduce((acc, obj) => {
        const value =
          obj.credit_type === CreditType.OLD
            ? oldCreditSetting.price
            : newCreditSetting.price;

        if (!acc[obj.credit_type]) {
          acc[obj.credit_type] = {
            creditType: obj.credit_type,
            quantity: {
              purchase: 0,
              use: 0,
              expired: 0,
              opening: 0,
              closing: 0,
            },
            value: { purchase: 0, use: 0, expired: 0, opening: 0, closing: 0 },
          };
        }

        acc[obj.credit_type].quantity.purchase += obj.buy_credit;
        acc[obj.credit_type].quantity.use += obj.use_credit;
        acc[obj.credit_type].quantity.expired += obj.expired_credit;
        acc[obj.credit_type].quantity.opening = obj.opening;
        acc[obj.credit_type].quantity.closing +=
          obj.opening + obj.buy_credit - obj.use_credit - obj.expired_credit;

        acc[obj.credit_type].value.purchase += obj.buy_credit * value;
        acc[obj.credit_type].value.use += obj.use_credit * value;
        acc[obj.credit_type].value.expired += obj.expired_credit * value;
        acc[obj.credit_type].value.opening = obj.opening * value;
        acc[obj.credit_type].value.closing +=
          (obj.opening + obj.buy_credit - obj.use_credit - obj.expired_credit) *
          value;

        return acc;
      }, {});

      if (isExport) {
        const periodData = this.formatPeriodType(
          Object.values(totalData),
          groupByMonth,
        );
        return [...dataExport, ...periodData];
      }

      return {
        data: groupByMonth,
        sum: Object.values(totalData),
      };
    } else if (type === OpeningClosingCreditType.SHOW_CUSTOMER) {
      if (isExport) {
        const dataPeriod = await this.getReportOpeningClosingCredit(
          req,
          {
            startDate,
            endDate,
            type: OpeningClosingCreditType.SHOW_PERIOD,
            page,
            limit,
            clientZoneName,
          },
          false,
        );

        const periodData = this.formatPeriodType(dataPeriod?.['sum']);
        let customerData = [];
        for (const month of dataPeriod?.['data']) {
          const monthData = this.formatPeriodType([], [month]);
          const reportTypeCustomer = await this.getReportOpeningClosingCredit(
            req,
            {
              startDate: moment(month.first_date)
                .startOf('month')
                .format('YYYY-MM-DDTHH:mm:ss.SSS'),
              endDate: moment(month.last_date)
                .endOf('month')
                .format('YYYY-MM-DDTHH:mm:ss.SSS'),
              type: OpeningClosingCreditType.SHOW_CUSTOMER,
              page,
              limit,
              keySearch,
              clientZoneName,
            },
            false,
          );

          const groupByCustomer = reportTypeCustomer?.['data'];

          customerData = [...customerData, ...monthData];
          for (
            let indexCustomer = 0;
            indexCustomer < groupByCustomer.length;
            indexCustomer++
          ) {
            const customer = groupByCustomer[indexCustomer];
            let i = 0;
            for (const credit of customer.credits) {
              customerData.push({
                title:
                  i === 0
                    ? `${customer.customerCode}   ${customer.firstName} ${customer.lastName}`
                    : null,
                opening: credit?.['quantity']?.['opening'].toFixed(2),
                purchase: credit?.['quantity']?.['purchase'].toFixed(2),
                use: credit?.['quantity']?.['use'].toFixed(2),
                closing: credit?.['quantity']?.['closing'].toFixed(2),
                expired: credit?.['quantity']?.['expired'].toFixed(2),
                type:
                  credit?.['creditType'] === 'credits'
                    ? 'Credit New'
                    : 'Credit Old',
                value_opening: credit?.['value']?.['opening'].toFixed(2),
                value_purchase: credit?.['value']?.['purchase'].toFixed(2),
                value_use: credit?.['value']?.['use'].toFixed(2),
                value_closing: credit?.['value']?.['closing'].toFixed(2),
                value_expired: credit?.['value']?.['expired'].toFixed(2),
                value_expiryDate: moment
                  .tz(customer.expiryDate, clientZoneName)
                  .subtract(1, 'day')
                  .format('DD/MM/YYYY'),
              });
              ++i;
            }
          }
        }
        return [...dataExport, ...periodData, ...customerData];
      }

      //paginate customer
      const qLimit = getCustomPaginationLimit(limit);
      const offset = (page - 1) * limit || 0;

      const queryCustomer = this.creditHistoryRepo
        .createQueryBuilder('credit_history')
        .select([
          'credit.customer AS customer_id',
          'MIN(customer.code) AS customer_code',
          'MIN(customer.firstName) AS customer_first_name',
          'MIN(customer.lastName) AS customer_last_name',
        ])
        .where('(credit_history.created BETWEEN :startTime AND :endTime)', {
          startTime: utcStartTime,
          endTime: utcEndTime,
        })
        .leftJoin('credit_history.credit', 'credit')
        .leftJoin('credit.customer', 'customer')
        .orderBy('customer.code', 'ASC');

      // Add search condition if keySearch is provided
      if (keySearch) {
        queryCustomer.andWhere(
          new Brackets((qb) => {
            qb.where('customer.code::text ILIKE :search', {
              search: `%${keySearch}%`,
            })
              .orWhere('customer.firstName ILIKE :search', {
                search: `%${keySearch}%`,
              })
              .orWhere('customer.lastName ILIKE :search', {
                search: `%${keySearch}%`,
              })
              .orWhere('customer.phone ILIKE :search', {
                search: `%${keySearch}%`,
              });
          }),
        );
      }

      queryCustomer.groupBy('credit.customer').addGroupBy('customer.code');

      if (!isExport) {
        queryCustomer.limit(qLimit).offset(offset);
      }

      const data = await queryCustomer.getRawMany();

      // Count query with the same conditions
      const countQuery = this.creditHistoryRepo
        .createQueryBuilder('credit_history')
        .select('COUNT(DISTINCT credit.customer)', 'count')
        .where('(credit_history.created BETWEEN :startTime AND :endTime)', {
          startTime: utcStartTime,
          endTime: utcEndTime,
        })
        .leftJoin('credit_history.credit', 'credit')
        .leftJoin('credit.customer', 'customer');

      // Apply the same search condition to the count query
      if (keySearch) {
        countQuery.andWhere(
          new Brackets((qb) => {
            qb.where('customer.code::text ILIKE :search', {
              search: `%${keySearch}%`,
            })
              .orWhere('customer.firstName ILIKE :search', {
                search: `%${keySearch}%`,
              })
              .orWhere('customer.lastName ILIKE :search', {
                search: `%${keySearch}%`,
              })
              .orWhere('customer.phone ILIKE :search', {
                search: `%${keySearch}%`,
              });
          }),
        );
      }

      const totalCustomers = await countQuery.getRawOne();

      const result = this.createPageInfo<any>(
        data,
        +totalCustomers.count,
        qLimit || +totalCustomers.count,
        offset || 0,
      );

      result.data = result.data.sort((a, b) => {
        if (a.customer_id < b.customer_id) return -1;
        if (a.customer_id > b.customer_id) return 1;
        return 0;
      });

      const customerIds = result.data.map((item) => item.customer_id);

      //get info credit from above list customer
      let queryCreditHistory =
        this.creditHistoryRepo.createQueryBuilder('credit_history');
      queryCreditHistory = queryCreditHistory
        .select([
          'MIN(credit_history.created) AS first_date',
          'MAX(credit_history.created) AS last_date',
          'SUM(CASE WHEN credit_history.isMembershipPkg = true THEN credit_history.paid ELSE 0 END) AS buy_credit',
          'SUM(CASE WHEN (credit_history.isMembershipPkg = false AND credit_history.isPurgeExpired = false) THEN credit_history.paid ELSE 0 END) AS use_credit',
          'SUM(CASE WHEN credit_history.isPurgeExpired = true THEN credit_history.paid ELSE 0 END) AS expired_credit',
          'credit_setting."creditType" AS credit_type',
          'credit.creditSetting AS credit_setting',
          'credit.customer AS customer',
          'credit.expiryDate AS expiry_date',
          'MIN(customer.code) AS customer_code',
          'MIN(customer.firstName) AS customer_first_name',
          'MIN(customer.lastName) AS customer_last_name',
          'credit.id AS credit_id',
          '(SELECT opening FROM credit_history AS ch2 WHERE ch2.opening = MIN(credit_history.opening) AND ch2."creditId" = credit.id LIMIT 1) AS opening',
          '(SELECT closing FROM credit_history AS ch2 WHERE ch2.closing = MAX(credit_history.closing) AND ch2."creditId" = credit.id LIMIT 1) AS closing',
          // '(SELECT closing FROM credit_history AS ch2 WHERE ch2.created = ( SELECT MAX(created) FROM credit_history WHERE "creditId" = "credit_history"."creditId" ) LIMIT 1) AS closing',
        ])
        .where('(credit_history.created BETWEEN :startTime AND :endTime)', {
          startTime: utcStartTime,
          endTime: utcEndTime,
        })
        .andWhere('credit.customer IN (:...customerIds)', { customerIds })
        .leftJoin('credit_history.credit', 'credit')
        .leftJoin('credit.creditSetting', 'credit_setting')
        .leftJoin('credit.customer', 'customer')
        .orderBy('customer.code', 'ASC')
        .groupBy('credit.id')
        .addGroupBy('"credit_setting"."creditType"')
        .addGroupBy('customer.code');

      const creditHistoryList = await queryCreditHistory.getRawMany();
      const oldCreditSetting = await this.creditSettingRepo.findOne({
        where: {
          creditType: CreditType.OLD,
        },
      });
      const newCreditSetting = await this.creditSettingRepo.findOne({
        where: {
          creditType: CreditType.NEW,
        },
      });
      const groupByCustomer = await creditHistoryList.reduce((acc, obj) => {
        const existingObj = acc.find(
          (item) => item.customerCode === obj.customer_code,
        );
        const value =
          obj.credit_type === CreditType.OLD
            ? oldCreditSetting.price
            : newCreditSetting.price;
        const closing =
          obj.opening + obj.buy_credit - obj.expired_credit - obj.use_credit;
        const newObj = {
          creditType: obj.credit_type,
          quantity: {
            purchase: obj.buy_credit,
            use: obj.use_credit,
            expired: obj.expired_credit,
            opening: obj.opening,
            closing: closing,
          },
          value: {
            purchase: obj.buy_credit * value,
            use: obj.use_credit * value,
            expired: obj.expired_credit * value,
            opening: obj.opening * value,
            closing: closing * value,
          },
        };

        if (existingObj) {
          existingObj.credits.push(newObj);
        } else {
          acc.push({
            customerCode: obj.customer_code,
            firstName: obj.customer_first_name,
            lastName: obj.customer_last_name,
            expiryDate: obj.expiry_date,
            credits: [newObj],
          });
        }
        return acc;
      }, []);

      return { ...result, data: groupByCustomer };
    } else if (type === OpeningClosingCreditType.SHOW_INVOICE) {
      const qLimit = getCustomPaginationLimit(limit);
      const offset = (page - 1) * limit || 0;
      const queryCreditHistory = this.creditHistoryRepo
        .createQueryBuilder('credit_history')
        .where('(credit_history.created BETWEEN :startTime AND :endTime)', {
          startTime: utcStartTime,
          endTime: utcEndTime,
        })
        .leftJoinAndSelect('credit_history.credit', 'credit')
        .leftJoinAndSelect('credit.creditSetting', 'credit_setting')
        .leftJoinAndSelect('credit.customer', 'customer');

      // Add search condition if keySearch is provided
      if (keySearch) {
        queryCreditHistory.andWhere(
          new Brackets((qb) => {
            qb.where("credit_history.detail->'code'::text ILIKE :search", {
              search: `%${keySearch}%`,
            })
              .orWhere('customer.code::text ILIKE :search', {
                search: `%${keySearch}%`,
              })
              .orWhere('customer.firstName ILIKE :search', {
                search: `%${keySearch}%`,
              })
              .orWhere('customer.lastName ILIKE :search', {
                search: `%${keySearch}%`,
              });
          }),
        );
      }

      queryCreditHistory.orderBy('credit_history.created', 'ASC');

      if (!isExport) {
        queryCreditHistory.limit(qLimit).offset(offset);
      }

      const [data, count] = await queryCreditHistory.getManyAndCount();

      const oldCreditSetting = await this.creditSettingRepo.findOne({
        where: {
          creditType: CreditType.OLD,
        },
      });
      const newCreditSetting = await this.creditSettingRepo.findOne({
        where: {
          creditType: CreditType.NEW,
        },
      });
      const mapData = data
        .map((item, index) => {
          const value =
            item?.credit?.creditSetting?.creditType === CreditType.OLD
              ? oldCreditSetting.price
              : newCreditSetting.price;
          return {
            created: item.created,
            order: index + 1,
            customer: item?.credit?.customer
              ? `${item.credit.customer.code} ${item.credit.customer.firstName} ${item.credit.customer.lastName}`
              : '',
            invoiceCode: item.isPurgeExpired
              ? null
              : item.isRefund
              ? `CN${item.detail?.['code']}`
              : `IN${item.detail?.['code']}`,
            invoiceCodeDisplay: item.isPurgeExpired
              ? null
              : item.isRefund
              ? `CN${item.detail?.['code']}`
              : `IN${item.detail?.['code']}`,
            creditType: item?.credit?.creditSetting?.creditType,
            isRefund: item.isRefund,
            quantity: {
              expired: item.isPurgeExpired ? item.paid : 0,
              purchase: item.isMembershipPkg ? item.paid : 0,
              use: item.isMembershipPkg || item.isPurgeExpired ? 0 : item.paid,
            },
            value: {
              expired: item.isPurgeExpired ? item.paid * value : 0,
              purchase: item.isMembershipPkg ? item.paid * value : 0,
              use:
                item.isMembershipPkg || item.isPurgeExpired
                  ? 0
                  : item.paid * value,
            },
          };
        })
        .filter(
          (record) =>
            record.invoiceCode !== null && record.invoiceCode !== undefined,
        );

      if (isExport) {
        // Get period data for summary information
        const dataPeriod = await this.getReportOpeningClosingCredit(req, {
          startDate,
          endDate,
          type: OpeningClosingCreditType.SHOW_PERIOD,
          page,
          limit,
          clientZoneName,
        });

        const periodData = this.formatPeriodType(dataPeriod?.['sum']);

        // Directly format the invoice data that's already filtered by the correct date range
        const invoiceData = mapData.map((invoice) => {
          // Ensure prefix 'IN' for all invoice records (unless refund or purge)
          let invoiceTitle = null;
          if (invoice.invoiceCodeDisplay) {
            if (invoice.isRefund) {
              invoiceTitle = invoice.invoiceCodeDisplay; // e.g., CNxxx
            } else if (
              invoice.invoiceCodeDisplay.startsWith('IN') ||
              invoice.invoiceCodeDisplay.startsWith('CN')
            ) {
              invoiceTitle = invoice.invoiceCodeDisplay;
            } else {
              invoiceTitle = `IN${invoice.invoiceCodeDisplay}`;
            }
          }

          return {
            title: invoiceTitle,
            opening: null,
            purchase: invoice?.['quantity']?.['purchase'].toFixed(2),
            use: invoice?.['quantity']?.['use'].toFixed(2),
            closing: null,
            expired: invoice?.['quantity']?.['expired'].toFixed(2),
            type:
              invoice.customer ||
              (invoice?.['creditType'] === 'credits'
                ? 'Credit New'
                : 'Credit Old'),
            value_opening: null,
            value_purchase: invoice?.['value']?.['purchase'].toFixed(2),
            value_use: invoice?.['value']?.['use'].toFixed(2),
            value_closing: null,
            value_expired: invoice?.['value']?.['expired'].toFixed(2),
            value_expiryDate: moment(invoice.created).format('DD/MM/YYYY'),
          };
        });

        return [...dataExport, ...periodData, ...invoiceData];
      }

      return this.createPageInfo<any>(
        mapData,
        count,
        qLimit || count,
        offset || 0,
      );
    }

    return {
      data: modifiedCreditHistoryList,
    };
  }

  private formatPeriodType(totalData: any, groupByMonth?: any) {
    const result = [];
    // handle total
    let i = 0;
    // for (const credit of totalData || []) {
    //   result.push({
    //     title: i === 0 ? 'TOTAL' : null,
    //     opening: credit?.['quantity']?.['opening'].toFixed(2),
    //     purchase: credit?.['quantity']?.['purchase'].toFixed(2),
    //     use: credit?.['quantity']?.['use'].toFixed(2),
    //     closing: credit?.['quantity']?.['closing'].toFixed(2),
    //     expired: credit?.['quantity']?.['expired'].toFixed(2),
    //     type:
    //       credit?.['creditType'] === 'credits' ? 'Credit New' : 'Credit Old',
    //     value_opening: credit?.['value']?.['opening'].toFixed(2),
    //     value_purchase: credit?.['value']?.['purchase'].toFixed(2),
    //     value_use: credit?.['value']?.['use'].toFixed(2),
    //     value_closing: credit?.['value']?.['closing'].toFixed(2),
    //     value_expired: credit?.['value']?.['expired'].toFixed(2),
    //     value_expiryDate: null,
    //   });
    //   ++i;
    // }

    //handle month
    for (const month of groupByMonth || []) {
      i = 0;
      for (const credit of month?.credits || []) {
        result.push({
          title:
            i === 0
              ? moment(month.month, 'MM-YYYY').format('MMMM YYYY').toUpperCase()
              : null,
          opening: credit?.['quantity']?.['opening'].toFixed(2),
          purchase: credit?.['quantity']?.['purchase'].toFixed(2),
          use: credit?.['quantity']?.['use'].toFixed(2),
          closing: credit?.['quantity']?.['closing'].toFixed(2),
          expired: credit?.['quantity']?.['expired'].toFixed(2),
          type:
            credit?.['creditType'] === 'credits' ? 'Credit New' : 'Credit Old',
          value_opening: credit?.['value']?.['opening'].toFixed(2),
          value_purchase: credit?.['value']?.['purchase'].toFixed(2),
          value_use: credit?.['value']?.['use'].toFixed(2),
          value_closing: credit?.['value']?.['closing'].toFixed(2),
          value_expired: credit?.['value']?.['expired'].toFixed(2),
          value_expiryDate: null,
        });
        ++i;
      }
    }
    return result;
  }

  async getReportNewCustomer(
    req: CrudRequest,
    { startDate, endDate, page, limit }: ReportQueryDTO,
    isExport = false,
  ) {
    const utcStartTime = startDate;
    const utcEndTime = endDate;
    const queryCustomer = this.customerRepo
      .createQueryBuilder('customer')
      .where('customer.firstVisit BETWEEN :startDate AND :endDate', {
        startDate: utcStartTime,
        endDate: utcEndTime,
      })
      .orderBy('customer.code', 'ASC');

    const qLimit = getCustomPaginationLimit(limit);
    const offset = (page - 1) * limit || 0;
    if (!isExport) {
      queryCustomer.take(qLimit).skip(offset);
    }
    const [CustomerList, total] = await queryCustomer.getManyAndCount();

    let modifiedInvoiceCustomer = [];
    if (CustomerList.length > 0) {
      const CustomerListPromises = CustomerList.map(async (customer, index) => {
        const invoicesOfCustomer = await this.invoiceRepo.find({
          where: {
            customer: {
              id: customer?.id,
            },
            status: In([InvoiceStatus.PAID]),
            created: MoreThanOrEqual(new Date(customer.firstVisit)),
          },
          order: {
            created: 'ASC',
          },
          relations: [
            'orders',
            'invoicePayments',
            'invoicePayments.paymentMethod',
            'invoiceCoupon',
          ],
        });
        // --- Refactored: Calculate discount per-invoice and log details ---
        let productSummary = 0;
        let serviceSummary = 0;
        let membershipSummary = 0;
        let foodBeverageSummary = 0;
        let couponSummary = 0;
        let sumPaid = new Decimal(0);
        let sumDeduction = new Decimal(0);
        let deduction = new Decimal(0); // credit value used for payment
        let totalPayment = new Decimal(0); // total payment by all methods
        const itemsData = [];
        for (const invoice of invoicesOfCustomer) {
          // Per-invoice type totals
          const invoiceTypeTotals = {
            product: 0,
            service: 0,
            membership: 0,
            coupon: 0,
            foodBeverage: 0,
          };
          let invoiceItemsTotal = 0;
          if (invoice?.orders?.length > 0) {
            for (const order of invoice.orders) {
              const orderPayload: any = order.payload;
              if (orderPayload && orderPayload.items) {
                const items: any[] = orderPayload.items;
                for (const item of items) {
                  const money =
                    (item?.quantity ?? 1) * (item?.product?.price ?? 0);
                  invoiceItemsTotal += money;
                  switch (item?.product?.type) {
                    case ProductType.PRODUCT:
                      invoiceTypeTotals.product += money;
                      break;
                    case ProductType.SERVICE:
                      invoiceTypeTotals.service += money;
                      break;
                    case ProductType.MEMBERSHIP:
                      invoiceTypeTotals.membership += money;
                      break;
                    case ProductType.COUPON:
                      invoiceTypeTotals.coupon += money;
                      break;
                    case ProductType.FOOD:
                    case ProductType.BEVERAGE:
                      invoiceTypeTotals.foodBeverage += money;
                      break;
                    default:
                      break;
                  }
                }
              }
            }
          }

          let invoiceCreditDeduction = new Decimal(0);
          let invoiceTotalPayment = new Decimal(0);
          if (invoice?.invoicePayments?.length > 0) {
            for (const payment of invoice.invoicePayments) {
              totalPayment = totalPayment.plus(new Decimal(payment.paid));
              invoiceTotalPayment = invoiceTotalPayment.plus(
                new Decimal(payment.paid),
              );
              if (
                payment?.paymentMethod?.name === 'Credits' ||
                payment?.paymentMethod?.name === 'Old Credits'
              ) {
                deduction = deduction.plus(new Decimal(payment.paid));
                invoiceCreditDeduction = invoiceCreditDeduction.plus(
                  new Decimal(payment.paid),
                );
              }
              sumPaid = sumPaid.plus(new Decimal(payment.paid));
            }
          }

          // Per-invoice discount calculation
          let couponPercent = 0;
          let invoiceDiscount = 0;
          if (invoice?.invoiceCoupon?.length > 0) {
            couponPercent = invoice.invoiceCoupon[0]?.percent || 0;
            invoiceDiscount = invoiceItemsTotal * (couponPercent / 100);
            sumDeduction = sumDeduction.plus(new Decimal(invoiceDiscount));
          }

          // Proportional discount for each type in this invoice
          function invoiceApplyDiscount(typeTotal) {
            if (!invoiceItemsTotal || !invoiceDiscount) return typeTotal;
            const discount = invoiceDiscount * (typeTotal / invoiceItemsTotal);
            return typeTotal - discount;
          }

          const productFinal = invoiceApplyDiscount(invoiceTypeTotals.product);
          const serviceFinal = invoiceApplyDiscount(invoiceTypeTotals.service);
          const membershipFinal = invoiceApplyDiscount(
            invoiceTypeTotals.membership,
          );
          const couponFinal = invoiceApplyDiscount(invoiceTypeTotals.coupon);
          const foodBeverageFinal = invoiceApplyDiscount(
            invoiceTypeTotals.foodBeverage,
          );

          // Sum up for the customer
          productSummary += productFinal;
          serviceSummary += serviceFinal;
          membershipSummary += membershipFinal;
          couponSummary += couponFinal;
          foodBeverageSummary += foodBeverageFinal;

          if (invoice?.code) {
            itemsData.push({
              referenceNo: isExport ? `IN${invoice.code}` : invoice.code,
            });
          }
        }

        const grossTotal =
          productSummary +
          serviceSummary +
          membershipSummary +
          couponSummary +
          foodBeverageSummary;

        const netTotal = grossTotal - Number(deduction);

        return {
          order: index + 1,
          customerCode: customer?.code,
          firstName: customer?.firstName,
          lastName: customer?.lastName,
          customerInfo:
            customer?.code +
            '   ' +
            customer?.firstName +
            ' ' +
            customer?.lastName,
          productSummary: productSummary.toFixed(2),
          serviceSummary: serviceSummary.toFixed(2),
          membershipSummary: membershipSummary.toFixed(2),
          foodBeverageSummary: foodBeverageSummary.toFixed(2),
          couponSummary: couponSummary.toFixed(2),
          itemsData,
          deduction: deduction.toFixed(2),
          total: netTotal.toFixed(2),
          payment: totalPayment.minus(deduction).toFixed(2),
        };
      });

      modifiedInvoiceCustomer = await Promise.all(CustomerListPromises);
    }
    // grand total by productSummary, serviceSummary, membershipSummary, foodBeverageSummary, couponSummary, total, payment
    const grandTotal = modifiedInvoiceCustomer.reduce(
      (total, item) => {
        total.productSummary += parseFloat(item.productSummary);
        total.serviceSummary += parseFloat(item.serviceSummary);
        total.membershipSummary += parseFloat(item.membershipSummary);
        total.foodBeverageSummary += parseFloat(item.foodBeverageSummary);
        total.couponSummary += parseFloat(item.couponSummary);
        total.total += parseFloat(item.total);
        total.payment += parseFloat(item.payment);
        total.deduction += parseFloat(item.deduction);
        return total;
      },
      {
        productSummary: 0,
        serviceSummary: 0,
        membershipSummary: 0,
        foodBeverageSummary: 0,
        couponSummary: 0,
        total: 0,
        payment: 0,
        deduction: 0,
      },
    );

    if (isExport) {
      modifiedInvoiceCustomer.push({
        ...grandTotal,
        productSummary: grandTotal.productSummary.toFixed(2),
        serviceSummary: grandTotal.serviceSummary.toFixed(2),
        membershipSummary: grandTotal.membershipSummary.toFixed(2),
        foodBeverageSummary: grandTotal.foodBeverageSummary.toFixed(2),
        couponSummary: grandTotal.couponSummary.toFixed(2),
        total: grandTotal.total.toFixed(2),
        payment: grandTotal.payment.toFixed(2),
        deduction: grandTotal.deduction.toFixed(2),
        sum: 'grand_total',
      });
      return modifiedInvoiceCustomer.map((item, index) => {
        return {
          ...item,
          itemsData:
            item.sum === 'grand_total'
              ? [{ referenceNo: 'GRAND TOTAL:' }]
              : item.itemsData,
        };
      });
    }
    return {
      ...this.createPageInfo<Customer>(
        modifiedInvoiceCustomer,
        total,
        qLimit || total,
        offset || 0,
      ),
      grandTotal,
    };
  }

  async getReportTreatmentHistory(
    req: CrudRequest,
    {
      keySearch,
      startDate,
      endDate,
      page,
      limit,
      clientZoneName,
    }: ReportQueryDTO,
    isExport = false,
  ) {
    const utcStartTime = startDate;
    const utcEndTime = endDate;
    const queryInvoice = this.invoiceRepo
      .createQueryBuilder('invoice')
      .leftJoinAndSelect('invoice.customer', 'customer')
      .where('invoice.created BETWEEN :startDate AND :endDate', {
        startDate: utcStartTime,
        endDate: utcEndTime,
      });
    // if (keySearch) {
    //   queryInvoice.andWhere(
    //     new Brackets((subQuery) =>
    //       subQuery
    //         .where(
    //           "(CONCAT(customer.firstName, ' ', customer.lastName) ILIKE :name)",
    //           { name: `%${keySearch}%` },
    //         )
    //         .orWhere('customer.code::text ILIKE :code', { code: keySearch }),
    //     ),
    //   );
    // }
    queryInvoice.orderBy('invoice.created', 'ASC');
    const InvoiceList = await queryInvoice.getMany();

    let modifiedInvoiceCustomer = [];
    if (InvoiceList.length > 0) {
      const CustomerListPromises = InvoiceList.map(async (invoice) => {
        const invoiceOfOrder = await this.invoiceRepo.findOne({
          where: {
            id: invoice?.id,
            orders: {
              items: {
                product: {
                  type: ProductType.SERVICE,
                },
              },
            },
          },
          relations: [
            'orders',
            'orders.items',
            'orders.items.product',
            'orders.items.employees',
          ],
        });
        const items = [];
        let count = 0;
        if (invoiceOfOrder?.orders?.length > 0) {
          for (const order of invoiceOfOrder?.orders) {
            for (const orderDetail of order?.items) {
              if (orderDetail?.product?.type === ProductType.SERVICE) {
                const objectService = {
                  treatedBy: orderDetail?.employees,
                  adjustment: orderDetail?.product.name,
                  quantity: orderDetail?.quantity,
                };
                items[count] = objectService;
                count++;
              }
            }
          }
        }

        return {
          date: invoice?.created,
          referenceNo: invoice?.code,
          customerCode: invoice?.customer?.code,
          customerFirstName: invoice?.customer?.firstName,
          customerLastName: invoice?.customer?.lastName,
          items,
        };
      });

      modifiedInvoiceCustomer = await Promise.all(CustomerListPromises);

      if (keySearch) {
        modifiedInvoiceCustomer = modifiedInvoiceCustomer.filter((item) => {
          const fullName =
            `${item?.customerFirstName} ${item?.customerLastName}`.toLowerCase();

          return (
            item?.items?.length > 0 &&
            ((item.customerCode &&
              item.customerCode
                .toLowerCase()
                .includes(keySearch.toLowerCase())) ||
              (item.customerFirstName &&
                item.customerFirstName
                  .toLowerCase()
                  .includes(keySearch.toLowerCase())) ||
              (item.customerLastName &&
                item.customerLastName
                  .toLowerCase()
                  .includes(keySearch.toLowerCase())) ||
              (fullName &&
                fullName.toLowerCase().includes(keySearch.toLowerCase())) ||
              item.items.some((subItem) => {
                return (
                  (subItem.adjustment &&
                    subItem.adjustment
                      .toLowerCase()
                      .includes(keySearch.toLowerCase())) ||
                  subItem.treatedBy.some((treatedBy) => {
                    return (
                      treatedBy.displayName &&
                      treatedBy.displayName
                        .toLowerCase()
                        .includes(keySearch.toLowerCase())
                    );
                  })
                );
              }))
          );
        });
      } else {
        modifiedInvoiceCustomer = modifiedInvoiceCustomer.filter((item) => {
          return item?.items?.length > 0;
        });
      }
    }
    if (isExport) {
      const dataExport = [];

      modifiedInvoiceCustomer.forEach((item, index) => {
        const customerInfo =
          item?.customerCode +
          '   ' +
          item?.customerFirstName +
          ' ' +
          item?.customerLastName;
        if (item.items.length > 0) {
          for (const productIndex in item.items) {
            const product = item.items[productIndex];
            dataExport.push({
              order: +productIndex === 0 ? index + 1 : '',
              date:
                +productIndex === 0
                  ? moment
                      .tz(item.date, clientZoneName)
                      .format('DD/MM/YYYY h:mm A')
                  : '',
              referenceNo: +productIndex === 0 ? `IN${item.referenceNo}` : '',
              customerInfo: +productIndex === 0 ? customerInfo : '',
              qty: product.quantity,
              treatedBy: product?.treatedBy
                .map((item) => item?.fullName || item?.displayName)
                .join([',']),
              adjusment: product.adjustment,
            });
          }
        } else {
          dataExport.push({
            order: index + 1,
            customerInfo,
            qty: item.items.length,
            treatedBy: '',
            adjusment: '',
          });
        }
      });

      return dataExport.map((item, index) => {
        return {
          order: index + 1,
          ...item,
        };
      });
    }

    return {
      data: modifiedInvoiceCustomer,
    };
  }
}
